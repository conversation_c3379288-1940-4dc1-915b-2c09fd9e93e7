# cloudbuild.yaml

# This file defines the steps for Cloud Build to build, tag, push, and deploy
# OrchestratorService and AgentService using dynamic tags.

substitutions:
  _TAG_NAME: 'latest' # Default tag, will be overridden by GitHub Actions
  _GCP_PROJECT_ID: 'agent-as-a-service-459620'
  _ARTIFACT_REGISTRY_REPO: 'agent-as-a-service'
  _REGION: 'us-west1'
  _SERVICE_ACCOUNT: 'projects/agent-as-a-service-459620/serviceAccounts/101640852106965308653' # Optional: if your Cloud Run needs a specific SA

serviceAccount: ${_SERVICE_ACCOUNT}

steps:  
# # Step 1: OrchestratorWebApp (currently commented out, can be enabled similarly)
# - name: 'gcr.io/cloud-builders/docker'
#   id: 'Build OrchestratorWebApp'
#   args: [
#     'build',
#     '-t',agent-as-a
#     '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorwebapp:${_TAG_NAME}',
#     '-t',
#     '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorwebapp:latest',
#     '.',
#     '-f',
#     'OrchestratorWebApp/Dockerfile',
#   ]
# - name: 'gcr.io/cloud-builders/docker'
#   id: 'Push OrchestratorWebApp'
#   args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorwebapp:${_TAG_NAME}']
# - name: 'gcr.io/cloud-builders/docker'
#   id: 'Push OrchestratorWebApp Latest'
#   args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorwebapp:latest']
# # Deployment for OrchestratorWebApp would typically be to Firebase Hosting as per your existing setup, not Cloud Run.

# Step 2: Build, Push, and Deploy OrchestratorService
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build OrchestratorService'
  args: [
    'build',
    '-t',
    '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:${_TAG_NAME}',
    '-t', # Also tag as latest
    '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:latest',
    '.', # Build context is the root of the repository
    '-f',
    'OrchestratorService/Dockerfile',
  ]

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push OrchestratorService by Tag'
  args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:${_TAG_NAME}']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push OrchestratorService Latest'
  args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:latest']

- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  id: 'Deploy OrchestratorService'
  entrypoint: gcloud  
  args:
    - 'run'
    - 'deploy'
    - 'orchestratorservice'
    - '--image'
    - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:${_TAG_NAME}'
    - '--region'
    - '${_REGION}'
    - '--project'
    - '${_GCP_PROJECT_ID}'
    - '--platform'
    - 'managed'
    - '--set-env-vars'
    - 'ASPNETCORE_URLS=http://*:$$PORT'
    # - '--service-account=${_SERVICE_ACCOUNT}' # Optional: if your service needs a specific SA
    - '--quiet'

# Step 3: Build, Push, and Deploy AgentService
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build AgentService'
  args: [
    'build',
    '-t',
    '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:${_TAG_NAME}',
    '-t', # Also tag as latest
    '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:latest',
    '.', # Build context is the root of the repository
    '-f',
    'AgentService/Dockerfile',
  ]

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push AgentService by Tag'
  args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:${_TAG_NAME}']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push AgentService Latest'
  args: ['push', '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:latest']

- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  id: 'Deploy AgentService'
  entrypoint: gcloud
  args:
    - 'run'
    - 'deploy'
    - 'agentservice'
    - '--image'
    - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:${_TAG_NAME}'
    - '--region'
    - '${_REGION}'
    - '--project'
    - '${_GCP_PROJECT_ID}'
    - '--platform'
    - 'managed'
    - '--set-env-vars'
    - 'ASPNETCORE_URLS=http://*:$$PORT'
    # - '--service-account=${_SERVICE_ACCOUNT}' # Optional
    - '--quiet'

# This section tells Cloud Build which images to push to Artifact Registry if not explicitly pushed.
# Since we have explicit push steps, this is mainly for build metadata.
images:
  - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:${_TAG_NAME}'
  - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/orchestratorservice:latest'
  - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:${_TAG_NAME}'
  - '${_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/agentservice:latest'

options:
  logging: CLOUD_LOGGING_ONLY # Changed from LEGACY to CLOUD_LOGGING_ONLY
  # machineType: 'E2_HIGHCPU_8' # Optional: Uncomment to specify a faster machine type for Cloud Build

