# See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/aspnet:8.0@sha256:c149fe7e2be3baccf3cc91e9e6cdcca0ce70f7ca30d5f90796d983ff4f27bd9a AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0@sha256:b56053d0a8f4627047740941396e76cd9e7a9421c83b1d81b68f10e5019862d7 AS build
WORKDIR /src
COPY ["OrchestratorWebApp/OrchestratorWebApp.csproj", "OrchestratorWebApp/"]
# Restore the WebAssembly workload
RUN dotnet workload restore "OrchestratorWebApp/OrchestratorWebApp.csproj"
RUN dotnet restore "OrchestratorWebApp/OrchestratorWebApp.csproj"
COPY . .
WORKDIR "/src/OrchestratorWebApp"
RUN dotnet build "OrchestratorWebApp.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "OrchestratorWebApp.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OrchestratorWebApp.dll"]
