//
// Actual devcontainer configuration.
//
// Empty except for reference to the prebuilt "base" container image. 
// This optimizes the devcontainer instance creation time (as well as separates the concerns of running from its configuration/building).
// Base image:
//  - config: ../.github/.devcontainer/devcontainer.json
//  - built: ../.github/workflows/prebuild.yml
//

// WARNING!: repo is currently PRIVATE, so the prebuilt image is not available unless logged in w/ an approriately-permissioned PAT.
// If you would like to use this image, please reach out and I can provide you with access.

{
    "name": "agentasaservice",
    // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
    "image": "ghcr.io/nam20485/agentasaservice:latest"
    // Features to add to the dev container. More info: https://containers.dev/features.
    // "features": {
    // },
    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    // "forwardPorts": [5000, 5001],
    // "portsAttributes": {
    //		"5001": {
    //			"protocol": "https"
    //		}
    // }
    // Use 'postCreateCommand' to run commands after the container is created.
    // "postCreateCommand": "dotnet restore",
    // Configure tool-specific properties.
    // "customizations": {
    //     "vscode": {
    //         "extensions": [
    //         ]
    //     }
    // }
    // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
    // "remoteUser": "root"
}