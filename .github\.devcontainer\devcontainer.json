// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/dotnet
{
	"name": "agentasaservice-prebuild-image",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "mcr.microsoft.com/devcontainers/dotnet:1-8.0-noble",
		
	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {
		"ghcr.io/devcontainers/features/github-cli:1": {},
		"ghcr.io/devcontainers/features/node:1" : {},
		"ghcr.io/devcontainers/features/docker-in-docker:2": {},
		// "ghcr.io/devcontainers/features/sshd:1": {},
		"ghcr.io/dhoeric/features/google-cloud-cli:1": {},
		"ghcr.io/devcontainers-extra/features/firebase-cli:2": {}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [5000, 5001],
	// "portsAttributes": {
	// 		"5001": {
	// 			"protocol": "https"
	// 		}
	// },

	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "dotnet workload restore OrchestratorWebApp/OrchestratorWebApp.csproj",

	// Configure tool-specific properties.
	"customizations": {
		"vscode": {
		   "extensions": [
			  "ms-dotnettools.csharp",
			  "ms-dotnettools.csharp-devkit",
			  "ms-dotnettools.csharp-extensions",
			  "ms-dotnettools.vscodeintellicode-csharp",
			  "github.copilot",
			  //"github.copilot-chat",               
			//   "github.vscode-pull-request-github",              
			   "42crunch.vscode-openapi",
			   "ahmadalli.vscode-nginx-conf",
			   "christian-kohler.npm-intellisense",
			//   "dbaeumer.vscode-eslint",
			//   "docsmsft.docs-article-templates",
			//   "docsmsft.docs-authoring-pack",
			//   "docsmsft.docs-build",
			//   "docsmsft.docs-images",
			//   "docsmsft.docs-markdown",
			//   "docsmsft.docs-preview",
			//   "docsmsft.docs-scaffolding",
			//   "docsmsft.docs-yaml",
			   "github.vscode-github-actions",
			   "googlecloudtools.cloudcode",
			   "hbenl.vscode-test-explorer",
			   "ms-dotnettools.dotnet-maui",
			   "ms-vscode.powershell",			
			//    "pspester.pester-test",
			//    "unoplatform.vscode"
		   ]
		}
	},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "vscode",
	// "containerUser": "vscode"
	
	//"updateRemoteUserUID": 
}


// 42crunch.vscode-openapi
// ahmadalli.vscode-nginx-conf
// christian-kohler.npm-intellisense
// dbaeumer.vscode-eslint
// docsmsft.docs-article-templates
// docsmsft.docs-authoring-pack
// docsmsft.docs-build
// docsmsft.docs-images
// docsmsft.docs-markdown
// docsmsft.docs-preview
// docsmsft.docs-scaffolding
// docsmsft.docs-yaml
// donjayamanne.githistory
// dotjoshjohnson.xml
// eamodio.gitlens
// fill-labs.dependi
// github.vscode-codeql
// github.vscode-github-actions
// gitpod.gitpod-remote-ssh
// googlecloudtools.cloudcode
// hbenl.vscode-test-explorer
// learningengineeringoperations.learn-training-ai-assistant
// ms-dotnettools.dotnet-maui
// ms-dotnettools.vscodeintellicode-csharp
// ms-toolsai.vscode-ai
// ms-toolsai.vscode-ai-remote
// ms-vscode.hexeditor
// ms-vscode.powershell
// ms-windows-ai-studio.windows-ai-studio
// pbkit.vscode-pbkit
// pdconsec.vscode-print
// sanjulaganepola.github-local-actions
// tamasfe.even-better-toml
// unoplatform.vscode
// visualstudioexptteam.intellicode-api-usage-examples
// visualstudioexptteam.vscodeintellicode
// visualstudioexptteam.vscodeintellicode-completions
// visualstudioexptteam.vscodeintellicode-insiders