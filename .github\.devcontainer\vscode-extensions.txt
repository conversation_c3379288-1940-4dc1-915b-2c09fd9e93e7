{
    "vscode": {
        "extensions": [
            "ms-dotnettools.csharp",
            "ms-dotnettools.csharp-devkit",
            "ms-dotnettools.csharp-extensions",
            //"ms-dotnettools.vscode-dotnet-runtime",
            "ms-dotnettools.vscodeintellicode-csharp",
            "github.copilot",
            "github.copilot-chat",
            "github.vscode-pull-request-github",
            "42crunch.vscode-openapi",
            "ahmadalli.vscode-nginx-conf",
            "christian-kohler.npm-intellisense",
            "dbaeumer.vscode-eslint",
            "docsmsft.docs-article-templates",
            "docsmsft.docs-authoring-pack",
            "docsmsft.docs-build",
            "docsmsft.docs-images",
            "docsmsft.docs-markdown",
            "docsmsft.docs-preview",
            "docsmsft.docs-scaffolding",
            "docsmsft.docs-yaml",
            //"donjayamanne.githistory",
            //"dotjoshjohnson.xml",
            //"eamodio.gitlens",
            //"fill-labs.dependi",
            //"github.vscode-codeql",
            "github.vscode-github-actions",
            "googlecloudtools.cloudcode",
            "hbenl.vscode-test-explorer",
            //"learningengineeringoperations.learn-training-ai-assistant",
            "ms-dotnettools.dotnet-maui",
            //"ms-toolsai.vscode-ai",
            //"ms-toolsai.vscode-ai-remote",
            //"ms-vscode.hexeditor",
            "ms-vscode.powershell",
            //"ms-windows-ai-studio.windows-ai-studio",
            //"pbkit.vscode-pbkit",
            //"pdconsec.vscode-print",
            //"sanjulaganepola.github-local-actions",
            //"tamasfe.even-better-toml",
            "unoplatform.vscode",
            //   "visualstudioexptteam.intellicode-api-usage-examples",
            //   "visualstudioexptteam.vscodeintellicode",
            //   "visualstudioexptteam.vscodeintellicode-completions",
            //   "visualstudioexptteam.vscodeintellicode-insiders"
        ]
    }
}