# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
on:
  push:
    branches:
      - main
permissions:
  checks: write
  contents: read
  pull-requests: write
jobs:
  build_and_deploy:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x' # Upgrade Node.js to a compatible version for firebase CLI

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x' # Using 8.0.x to get the latest patch

      - name: Authenticate to Google Cloud
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_AGENT_AS_A_SERVICE_459620 }}

      - name: Set up Cloud SDK
        uses: 'google-github-actions/setup-gcloud@v2'

      - name: Submit Cloud Build for Services (Main)
        run: |
          gcloud beta builds submit --quiet --project=${{ steps.auth.outputs.project_id }} --config=cloudbuild.yaml --substitutions=_TAG_NAME=latest # For main branch, we might use 'latest' or a release tag
        working-directory: . # Assuming cloudbuild.yaml is at the root

      - name: Build and publish Blazor app
        run: ./scripts/publish-orchestratorwebapp.ps1 # Ensure this script is executable: chmod +x ./scripts/publish-orchestratorwebapp.ps1

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_AGENT_AS_A_SERVICE_459620 }}
          channelId: live+
          projectId: agent-as-a-service-459620
