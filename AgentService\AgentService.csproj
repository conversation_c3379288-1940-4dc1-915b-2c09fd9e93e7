﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>73429643-7994-402c-a58f-b8a56c53dc85</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.10.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="Google.Cloud.AIPlatform.V1" Version="3.31.0" />
    <PackageReference Include="Google.Cloud.SecretManager.V1" Version="2.5.0" />
    <PackageReference Include="Octokit" Version="14.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SharedLib\SharedLib.csproj" />
  </ItemGroup>

</Project>
