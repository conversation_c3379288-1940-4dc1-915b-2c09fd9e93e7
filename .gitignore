# .gitignore for Visual Studio Projects
# Build results
[Bb]in/
[Oo]bj/

# User-specific files
*.user
*.suo
*.userosscache
*.sln.docstates

# Mono Auto Generated Files
mono_crash.*

# Windows image file caches
Thumbs.db
*.DS_Store

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# DotNet specific
project.lock.json
project.fragment.lock.json
artifacts/

# Visual Studio Code
.vscode/

# Others
*.log
*.vspscc
*.vssscc
*.ncb
*.sbr
*.scc

# Visual Studio 2017+ cache/options directory
.vs/

# Dotnet CLI
*.dbmdl
*.jfm

# Azure Pipelines
.ionide/

# Rider
.idea/
*.sln.iml

# Local history for Visual Studio
.localhistory/

# Backup & report files from converting an old project file
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl

# Microsoft Fakes
FakesAssemblies/

# Node.js Tools for Visual Studio
node_modules/

# Yarn
.yarn/
.yarnrc.yml

# End of .gitignore
OrchestratorWebApp/app/
OrchestratorWebApp/publish/
AgentService/publish/AgentService.deps.json
AgentService/publish/AgentService.dll
AgentService/publish/AgentService.pdb
AgentService/publish/AgentService.runtimeconfig.json
AgentService/publish/appsettings.Development.json
AgentService/publish/appsettings.json
AgentService/publish/FirebaseAdmin.dll
AgentService/publish/Google.Api.CommonProtos.dll
AgentService/publish/Google.Api.Gax.dll
AgentService/publish/Google.Api.Gax.Grpc.dll
AgentService/publish/Google.Api.Gax.Rest.dll
AgentService/publish/Google.Apis.Auth.dll
AgentService/publish/Google.Apis.Core.dll
AgentService/publish/Google.Apis.dll
AgentService/publish/Google.Cloud.AIPlatform.V1.dll
AgentService/publish/Google.Cloud.Firestore.dll
AgentService/publish/Google.Cloud.Firestore.V1.dll
AgentService/publish/Google.Cloud.Iam.V1.dll
AgentService/publish/Google.Cloud.Location.dll
AgentService/publish/Google.Cloud.SecretManager.V1.dll
AgentService/publish/Google.LongRunning.dll
AgentService/publish/Google.Protobuf.dll
AgentService/publish/Grpc.Auth.dll
AgentService/publish/Grpc.Core.Api.dll
AgentService/publish/Grpc.Net.Client.dll
AgentService/publish/Grpc.Net.Common.dll
AgentService/publish/Microsoft.AspNetCore.OpenApi.dll
AgentService/publish/Microsoft.Bcl.AsyncInterfaces.dll
AgentService/publish/Microsoft.OpenApi.dll
AgentService/publish/Newtonsoft.Json.dll
AgentService/publish/Octokit.dll
AgentService/publish/SharedLib.dll
AgentService/publish/SharedLib.pdb
AgentService/publish/Swashbuckle.AspNetCore.Swagger.dll
AgentService/publish/Swashbuckle.AspNetCore.SwaggerGen.dll
AgentService/publish/Swashbuckle.AspNetCore.SwaggerUI.dll
AgentService/publish/System.CodeDom.dll
AgentService/publish/System.Linq.Async.dll
AgentService/publish/System.Management.dll
AgentService/publish/web.config
AgentService/publish/runtimes/win/lib/net7.0/System.Management.dll
OrchestratorService/publish/appsettings.Development.json
OrchestratorService/publish/appsettings.json
OrchestratorService/publish/FirebaseAdmin.dll
OrchestratorService/publish/Google.Api.CommonProtos.dll
OrchestratorService/publish/Google.Api.Gax.dll
OrchestratorService/publish/Google.Api.Gax.Grpc.dll
OrchestratorService/publish/Google.Api.Gax.Rest.dll
OrchestratorService/publish/Google.Apis.Auth.dll
OrchestratorService/publish/Google.Apis.Core.dll
OrchestratorService/publish/Google.Apis.dll
OrchestratorService/publish/Google.Cloud.Firestore.V1.dll
OrchestratorService/publish/Google.Cloud.Iam.V1.dll
OrchestratorService/publish/Google.Cloud.Location.dll
OrchestratorService/publish/Google.Cloud.SecretManager.V1.dll
OrchestratorService/publish/Google.LongRunning.dll
OrchestratorService/publish/Google.Protobuf.dll
OrchestratorService/publish/Grpc.Auth.dll
OrchestratorService/publish/Grpc.Core.Api.dll
OrchestratorService/publish/Grpc.Net.Client.dll
OrchestratorService/publish/Grpc.Net.Common.dll
OrchestratorService/publish/Microsoft.AspNetCore.OpenApi.dll
OrchestratorService/publish/Microsoft.Bcl.AsyncInterfaces.dll
OrchestratorService/publish/Microsoft.OpenApi.dll
OrchestratorService/publish/Newtonsoft.Json.dll
OrchestratorService/publish/Octokit.dll
OrchestratorService/publish/OrchestratorService.deps.json
OrchestratorService/publish/OrchestratorService.dll
OrchestratorService/publish/OrchestratorService.pdb
OrchestratorService/publish/OrchestratorService.runtimeconfig.json
OrchestratorService/publish/SharedLib.dll
OrchestratorService/publish/SharedLib.pdb
OrchestratorService/publish/Swashbuckle.AspNetCore.Swagger.dll
OrchestratorService/publish/Swashbuckle.AspNetCore.SwaggerGen.dll
OrchestratorService/publish/Swashbuckle.AspNetCore.SwaggerUI.dll
OrchestratorService/publish/System.CodeDom.dll
OrchestratorService/publish/System.Management.dll
OrchestratorService/publish/web.config
OrchestratorService/publish/runtimes/win/lib/net7.0/System.Management.dll
