create a standard .NET visual studio git ignore file

create a vs code build config for the solution

checkout development branch

create another project simiar to the first once calls OrchestratorService

rebuild the solutojh to validate

add a Blazor WebAssembly project called OrchestratorWebApp

Create a .NET STandard 2.0 shared library project called SharedLib, add it ot the soluton, and add project references to it from the two *Service projects

commit the changes

Modify your commit command to enable signing

push the commit

Is there any type of file or file extension that is a file that contians a list of promtps to execute

I dont understand- what does a markdown have to do with my request? Where is the file you are reffering to?

Forget about markdown file I don't know where you got that from. So here is the question- has anyone come up wioth a file format or tool that takes as input as list of ge AI prompt strings for the pruposes of prompting a gen ai agent to execute them?

Gotcha thats cool. Ill have to ask <PERSON> to do a research report for me on that subject.

I want two files created:

1. The list of all commands you executed at the behest of my prompts in this session. Each on their own line. You can logically sepeerate the groups of commands that went together by grouping them without blank lines in between them, and then sperate groups of commands by adding a blank line after a group. This file shoudl be able to be executed in either pwsh, bash, or cmd.

2.) A list of all the prompts I asked you to execute. As strings seperated by balnk lines.
