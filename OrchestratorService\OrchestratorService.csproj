<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aa55393c-c913-487f-8e11-24146423234f</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="Google.Cloud.SecretManager.V1" Version="2.5.0" />
    <PackageReference Include="Google.Cloud.Firestore.V1" Version="3.10.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SharedLib\SharedLib.csproj" />
  </ItemGroup>

</Project>
