---
name: Pre-build dev container image

on:
  push:
    branches:
      - main
      - development
jobs:
  prebuild:
    permissions:
      contents: read
      packages: write

    runs-on: ubuntu-22.04
    steps:
        
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Login to GitHub Container Registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Pre-build dev container image
        uses: devcontainers/ci@8bf61b26e9c3a98f69cb6ce2f88d24ff59b785c6
        with:
          subFolder: .github
          imageName: ghcr.io/nam20485/agentasaservice
          cacheFrom: ghcr.io/nam20485/agentasaservice
          push: always
