# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR
on: pull_request
permissions:
  checks: write
  contents: read
  pull-requests: write
jobs:
  build_and_preview:
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x' # Upgrade Node.js to a compatible version for firebase CLI    

      - name: Install Firebase CLI
        run: npm install firebase-tools@14.4.0

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.410' # Using 8.0.x to get the latest patch

      - name: Authenticate to Google Cloud
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_AGENT_AS_A_SERVICE_459620 }}'

      - name: Set up Cloud SDK
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: beta

      - name: Submit Cloud Build for Services (Preview)
        run: |
          gcloud beta builds submit --quiet --project=${{ steps.auth.outputs.project_id }} --config=cloudbuild.yaml --substitutions=_TAG_NAME=${{ github.sha }}
        working-directory: . # Assuming cloudbuild.yaml is at the root        

      #
      # OrchestratorWebApp
      #
      - name: Build and publish Blazor app
        run: |
          chmod +x ./scripts/publish-orchestratorwebapp.ps1
          ./scripts/publish-orchestratorwebapp.ps1

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_AGENT_AS_A_SERVICE_459620 }}
          projectId: agent-as-a-service-459620
