﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>76bca866-35d5-4d13-b7ab-323080705067</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.16" PrivateAssets="all" />
  </ItemGroup>

  <Target Name="Install/Update Workload wasm-tools" BeforeTargets="PreBuildEvent">
    <PropertyGroup>
      <WasmToolsWorkloadId>wasm-tools</WasmToolsWorkloadId>
      <_WasmToolsInstalled>false</_WasmToolsInstalled> <!-- Default to not installed -->
    </PropertyGroup>
    <Message Text="Checking if .NET workload '$(WasmToolsWorkloadId)' is installed..." Importance="high" />
    <!-- Execute 'dotnet workload list' and capture its output -->
    <Exec Command="dotnet workload list" ConsoleToMSBuild="true" IgnoreStandardErrorWarningFormat="true" ContinueOnError="true">
      <Output TaskParameter="ConsoleOutput" ItemName="WorkloadListOutput" />
    </Exec>
    <!-- Filter the output lines to find if 'wasm-tools' is listed -->
    <ItemGroup>
      <_WasmToolsLine Include="@(WorkloadListOutput)" Condition="$([System.String]::new('%(Identity)').Contains('$(WasmToolsWorkloadId)'))" />
    </ItemGroup>
    <!-- If _WasmToolsLine item group is not empty, the workload was found in the list -->
    <PropertyGroup>
      <_WasmToolsInstalled Condition="'@(_WasmToolsLine)' != ''">true</_WasmToolsInstalled>
    </PropertyGroup>
    <Message Text=".NET workload '$(WasmToolsWorkloadId)' installed status: $(_WasmToolsInstalled). Will attempt install if false." Importance="high" />
    <!-- Conditionally run the install command -->
    <Exec Command="dotnet workload install $(WasmToolsWorkloadId)" Condition="'$(_WasmToolsInstalled)' == 'false'" />
    <!-- <Exec Command="dotnet workload update Condition="'$(_WasmToolsInstalled)' == 'true'" /> -->
  </Target>

</Project>
